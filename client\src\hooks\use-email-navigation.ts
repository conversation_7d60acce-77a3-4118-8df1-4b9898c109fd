import { useEmailDetail } from '@/context/EmailDetailContext';
import { useEmailList } from '@/context/EmailListContext';
import type { Email } from '@/types/email';

/**
 * Custom hook for email navigation
 * Handles moving between emails, getting current indexes, etc.
 */
export function useEmailNavigation() {
  const { emails } = useEmailList();
  const { selectedEmail, selectEmail } = useEmailDetail();

  /**
   * Navigate back to the email list view
   */
  const navigateBack = () => {
    selectEmail(null);
  };

  /**
   * Navigate to the next email in the list
   */
  const navigateNext = () => {
    if (!selectedEmail || emails.length === 0) return;

    const currentIndex = emails.findIndex((email) => email.messageId === selectedEmail.messageId);
    const nextIndex = (currentIndex + 1) % emails.length;
    const nextEmail = emails[nextIndex];
    if (nextEmail?.messageId) {
      selectEmail(nextEmail.messageId);
    }
  };

  /**
   * Navigate to the previous email in the list
   */
  const navigatePrevious = () => {
    if (!selectedEmail || emails.length === 0) return;

    const currentIndex = emails.findIndex((email) => email.messageId === selectedEmail.messageId);
    const prevIndex = (currentIndex - 1 + emails.length) % emails.length;
    const prevEmail = emails[prevIndex];
    if (prevEmail?.messageId) {
      selectEmail(prevEmail.messageId);
    }
  };

  /**
   * Get the current index of the selected email
   */
  const getCurrentIndex = () => {
    if (!selectedEmail || emails.length === 0) return 0;
    return emails.findIndex((email) => email.messageId === selectedEmail.messageId) + 1;
  };

  /**
   * Get the total number of emails
   */
  const getTotalCount = () => {
    return emails.length;
  };

  /**
   * Check if the email is selected
   */
  const isEmailSelected = (email: Email) => {
    return selectedEmail?.id === email.id;
  };

  return {
    navigateBack,
    navigateNext,
    navigatePrevious,
    getCurrentIndex,
    getTotalCount,
    isEmailSelected,
    selectedEmail,
    selectEmail,
  };
}
