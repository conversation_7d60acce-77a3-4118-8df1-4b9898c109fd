import { fireEvent, render, screen } from '@testing-library/react';
import type { Email } from '@/types/email';
import type React from 'react';
import EmailList from '@/components/email/EmailList';
import '@testing-library/jest-dom';

// ------------------ mocks ------------------
const mockNextPage = jest.fn();
const mockPrevPage = jest.fn();
const mockGoToPage = jest.fn();
const mockRefresh = jest.fn();

const sampleEmails: Email[] = [
  {
    id: 1,
    sender: 'Alice',
    subject: 'Meeting',
    snippet: 'Let us meet tomorrow',
    receivedAt: new Date().toISOString(),
  } as unknown as Email,
];

// Will be reassigned in individual tests to emulate different states
const mockUseEmailList = jest.fn();

jest.mock('@/context/EmailListContext', () => ({
  __esModule: true,
  useEmailList: (...args: any[]) => mockUseEmailList(...args),
}));

jest.mock('@/context/EmailDetailContext', () => ({
  __esModule: true,
  useEmailDetail: () => ({ selectedEmail: null, selectEmail: jest.fn() }),
}));

jest.mock('@/hooks/use-mobile', () => ({
  __esModule: true,
  useIsMobile: () => false,
}));

jest.mock('@/hooks/use-stats', () => ({
  __esModule: true,
  useStats: () => ({ stats: { totalEmails: 1, readEmails: 0 } }),
}));

// Mock VirtualizedEmailList to avoid complex internals and Radix portals
jest.mock('@/components/email/VirtualizedEmailList', () => {
  const React = require('react');
  return {
    __esModule: true,
    default: ({ emails, isLoading }: { emails: any[]; isLoading?: boolean }) => {
      if (isLoading) {
        return (
          <div className="flex justify-center items-center h-full">
            <div className="h-8 w-8 animate-spin text-primary" data-testid="loading-spinner">Loading...</div>
          </div>
        );
      }

      if (!emails || emails.length === 0) {
        return (
          <div className="flex flex-col justify-center items-center h-full text-gray-500 p-8">
            <p className="text-lg">No emails found</p>
            <p className="text-sm mt-2">Try adjusting your filters or search criteria</p>
          </div>
        );
      }

      return (
        <div data-testid="virtualized-list">
          {emails.map((email) => (
            <div key={email.id} data-testid="email-item">
              {email.subject}
            </div>
          ))}
        </div>
      );
    },
  };
});

jest.mock('@/components/email/EmailItem', () => ({
  __esModule: true,
  default: (props: any) => <div data-testid="email-item">{props.email.subject}</div>,
}));

jest.mock('@/components/email/EmailFilter', () => ({
  __esModule: true,
  default: () => <div data-testid="email-filter" />,
  defaultFilters: { status: 'all', priority: 'all', categories: [], timeRange: 'all' },
}));
jest.mock('@/components/email/EmailSearch', () => ({
  __esModule: true,
  default: () => <div data-testid="email-search" />,
}));
jest.mock('@/components/email/EmailPagination', () => {
  const React = require('react');
  const { useEmailList } = require('@/context/EmailListContext');
  const MockPagination = () => {
    const { nextPage, prevPage } = useEmailList();
    return (
      <div data-testid="pagination">
        <button aria-label="Previous page" onClick={prevPage} />
        <button aria-label="Next page" onClick={nextPage} />
      </div>
    );
  };
  return {
    __esModule: true,
    default: MockPagination,
  };
});

// ------------------ Radix Dialog Mock ------------------
jest.mock('@radix-ui/react-dialog', () => {
  const React = require('react');
  const Stub = React.forwardRef((props: any, ref: any) => <div ref={ref} {...props} />);
  return {
    __esModule: true,
    Root: Stub,
    Trigger: Stub,
    Close: Stub,
    Portal: ({ children }: { children: React.ReactNode }) => <div>{children}</div>,
    Overlay: Stub,
    Content: Stub,
    Title: Stub,
    Description: Stub,
  };
});

describe('Feature/EmailList component', () => {
  beforeEach(() => {
    mockUseEmailList.mockReset();
    mockUseEmailList.mockReturnValue({
      filteredEmails: sampleEmails,
      isLoading: false,
      refreshEmails: mockRefresh,
      filters: { status: 'all', priority: 'all', categories: [], timeRange: 'all' },
      setFilters: jest.fn(),
      searchQuery: '',
      setSearchQuery: jest.fn(),
      currentPage: 1,
      totalPages: 2,
      nextPage: mockNextPage,
      prevPage: mockPrevPage,
      goToPage: mockGoToPage,
    });
  });

  it('renders email items from context', () => {
    render(<EmailList />);
    expect(screen.getAllByTestId('email-item').length).toBe(1);
    expect(screen.getByText('Meeting')).toBeInTheDocument();
  });

  it('calls pagination nextPage when next button clicked', () => {
    render(<EmailList />);
    fireEvent.click(screen.getByLabelText(/next page/i));
    expect(mockNextPage).toHaveBeenCalled();
  });

  it('displays loading spinner when loading', () => {
    mockUseEmailList.mockReturnValue({
      filteredEmails: [],
      isLoading: true,
      refreshEmails: mockRefresh,
      filters: { status: 'all', priority: 'all', categories: [], timeRange: 'all' },
      setFilters: jest.fn(),
      searchQuery: '',
      setSearchQuery: jest.fn(),
      currentPage: 1,
      totalPages: 1,
      nextPage: jest.fn(),
      prevPage: jest.fn(),
      goToPage: jest.fn(),
    });
    render(<EmailList />);
    // VirtualizedEmailList shows a spinner when loading, not skeletons
    expect(document.querySelector('.animate-spin')).toBeInTheDocument();
  });

  it('shows empty state when no emails', () => {
    mockUseEmailList.mockReturnValue({
      filteredEmails: [],
      isLoading: false,
      refreshEmails: mockRefresh,
      filters: { status: 'all', priority: 'all', categories: [], timeRange: 'all' },
      setFilters: jest.fn(),
      searchQuery: '',
      setSearchQuery: jest.fn(),
      currentPage: 1,
      totalPages: 1,
      nextPage: jest.fn(),
      prevPage: jest.fn(),
      goToPage: jest.fn(),
    });
    render(<EmailList />);
    // VirtualizedEmailList shows "No emails found" text
    expect(screen.getByText(/no emails found/i)).toBeInTheDocument();
  });
}); 